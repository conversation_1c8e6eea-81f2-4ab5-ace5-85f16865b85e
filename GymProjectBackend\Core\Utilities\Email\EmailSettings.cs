using System;

namespace Core.Utilities.Email
{
    /// <summary>
    /// E-posta ayarları configuration modeli
    /// Environment'a göre farklı ayarlar destekler (dev, staging, canlı)
    /// </summary>
    public class EmailSettings
    {
        /// <summary>
        /// SendGrid API anahtarı
        /// </summary>
        public string SendGridApiKey { get; set; }

        /// <summary>
        /// Gönderen e-posta adresi
        /// </summary>
        public string FromEmail { get; set; }

        /// <summary>
        /// Gönderen adı
        /// </summary>
        public string FromName { get; set; }

        /// <summary>
        /// Şifre sıfırlama e-posta template ID'si (SendGrid Dynamic Template)
        /// </summary>
        public string PasswordResetTemplateId { get; set; }

        /// <summary>
        /// Hoş geldin e-posta template ID'si (SendGrid Dynamic Template)
        /// </summary>
        public string WelcomeTemplateId { get; set; }

        /// <summary>
        /// E-posta ayarlarının geç<PERSON><PERSON> olup olmadığını kontrol eder
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(SendGridApiKey) &&
                   !string.IsNullOrEmpty(FromEmail) &&
                   !string.IsNullOrEmpty(FromName);
        }
    }
}
