using Core.DataAccess;
using Core.Entities.Concrete;
using System;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Şifre sıfırlama token'ları için Data Access Layer interface'i
    /// SOLID prensiplerine uygun olarak tasarlandı
    /// </summary>
    public interface IPasswordResetTokenDal : IEntityRepository<PasswordResetToken>
    {
        /// <summary>
        /// Token string'ine göre geçerli token'ı getirir
        /// </summary>
        /// <param name="token">Token string'i</param>
        /// <returns>Geçerli token veya null</returns>
        PasswordResetToken GetValidTokenByTokenString(string token);

        /// <summary>
        /// Kullanıcının aktif (kullanılmamış ve süresi dolmamış) token'larını getirir
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>Aktif token'lar listesi</returns>
        List<PasswordResetToken> GetActiveTokensByUserId(int userId);

        /// <summary>
        /// Kullanıcının tüm aktif token'larını pasif hale getirir
        /// Yeni token oluşturulmadan önce eski token'ları geçersiz kılmak için kullanılır
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="usedIpAddress">İşlemi yapan IP adresi</param>
        void DeactivateAllUserTokens(int userId, string usedIpAddress = null);

        /// <summary>
        /// Süresi dolmuş token'ları temizler
        /// Performance optimizasyonu için kullanılır
        /// </summary>
        /// <param name="olderThanDays">Kaç gün önceki token'ları temizleyeceği (varsayılan: 7)</param>
        /// <returns>Temizlenen token sayısı</returns>
        int CleanupExpiredTokens(int olderThanDays = 7);

        /// <summary>
        /// Belirli bir IP adresinden gelen token isteklerini sayar
        /// Rate limiting ve güvenlik için kullanılır
        /// </summary>
        /// <param name="ipAddress">IP adresi</param>
        /// <param name="sinceDate">Hangi tarihten itibaren sayılacağı</param>
        /// <returns>Token istek sayısı</returns>
        int CountTokenRequestsByIp(string ipAddress, DateTime sinceDate);
    }
}
