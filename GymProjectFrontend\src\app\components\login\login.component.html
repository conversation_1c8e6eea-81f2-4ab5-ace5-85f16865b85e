<div class="login-container">
  <div class="login-wrapper">
    <!-- Left Panel with Gym Image -->
    <div class="login-image-panel">
      <div class="overlay">
        <div class="gym-branding">
          <div class="logo-container">
            <i class="fas fa-dumbbell"></i>
          </div>
          <h1>GymKod Pro</h1>
          <p>Profesyonel Spor Salonu Yönetim Sistemi</p>
          <div class="features">
            <div class="feature">
              <i class="fas fa-users"></i>
              <span>Üye Yönetimi</span>
            </div>
            <div class="feature">
              <i class="fas fa-calendar-check"></i>
              <span>Program Takibi</span>
            </div>
            <div class="feature">
              <i class="fas fa-chart-line"></i>
              <span>Performans Analizi</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Panel with Login Form -->
    <div class="login-form-panel">
      <div class="login-form-container">
        <div class="login-header">
          <div class="header-icon">
            <i class="fas fa-user-shield"></i>
          </div>
          <h2>Yönetim Paneli</h2>
          <p>Hesabınıza giriş yapın</p>
        </div>
        
        <form [formGroup]="loginForm" (ngSubmit)="login($event)" class="login-form">
          <div class="form-group">
            <label for="email">E-posta</label>
            <div class="input-group">
              <i class="fas fa-envelope"></i>
              <input 
                id="email"
                type="email" 
                formControlName="email" 
                placeholder="E-posta adresinizi girin"
                [ngClass]="{'is-invalid': loginForm.get('email')?.invalid && loginForm.get('email')?.touched}"
              >
            </div>
            <div class="error-message" *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
              <small *ngIf="loginForm.get('email')?.errors?.['required']">E-posta adresi gerekli</small>
              <small *ngIf="loginForm.get('email')?.errors?.['email']">Geçerli bir e-posta adresi girin</small>
            </div>
          </div>

          <div class="form-group">
            <label for="password">Şifre</label>
            <div class="input-group">
              <i class="fas fa-lock"></i>
              <input 
                id="password"
                [type]="passwordVisible ? 'text' : 'password'"
                formControlName="password"
                placeholder="Şifrenizi girin"
                [ngClass]="{'is-invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
              >
              <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                <i class="fas" [class.fa-eye-slash]="passwordVisible" [class.fa-eye]="!passwordVisible"></i>
              </button>
            </div>
            <div class="error-message" *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
              <small>Şifre gerekli</small>
            </div>
          </div>

          <div class="form-actions">
            <a routerLink="/forgot-password" class="forgot-password">Şifremi unuttum</a>
          </div>

          <button 
            type="submit" 
            [disabled]="loginForm.invalid || isLoading"
            class="login-button"
          >
            <span *ngIf="!isLoading">Giriş Yap</span>
            <app-loading-spinner *ngIf="isLoading" [size]="'small'" [showText]="false"></app-loading-spinner>
          </button>
        </form>
        
        <div class="login-footer">
          <div class="support">
            <i class="fas fa-headset"></i>
            <span>Destek: <a href="mailto:support&#64;gymkod.com">support&#64;gymkod.com</a></span>
          </div>
          <p>© 2025 GymKod Pro. Tüm hakları saklıdır.</p>
        </div>
      </div>
    </div>
  </div>
</div>
