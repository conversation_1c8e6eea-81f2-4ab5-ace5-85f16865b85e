using Core.Utilities.Results;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Core.Utilities.Monitoring
{
    /// <summary>
    /// Database performans izleme servisi implementation
    /// 1000+ salon için kritik performans metrikleri
    /// </summary>
    public class DatabaseMonitoringService : IDatabaseMonitoringService
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseMonitoringService> _logger;

        public DatabaseMonitoringService(IConfiguration configuration, ILogger<DatabaseMonitoringService> logger)
        {
            var environment = configuration["Environment"] ?? "dev";
            _connectionString = configuration.GetConnectionString(environment);
            _logger = logger;
        }

        public async Task<IDataResult<int>> GetActiveConnectionCountAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT COUNT(*) as ActiveConnections
                    FROM sys.dm_exec_sessions 
                    WHERE is_user_process = 1 
                    AND status = 'running'";

                using var command = new SqlCommand(query, connection);
                var result = await command.ExecuteScalarAsync();

                return new SuccessDataResult<int>(Convert.ToInt32(result), "Aktif connection sayısı başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Aktif connection sayısı alınırken hata oluştu");
                return new ErrorDataResult<int>(0, "Aktif connection sayısı alınamadı.");
            }
        }

        public async Task<IDataResult<ConnectionPoolStatus>> GetConnectionPoolStatusAsync()
        {
            try
            {
                // Connection string'den pool ayarlarını parse et
                var builder = new SqlConnectionStringBuilder(_connectionString);
                var maxPoolSize = builder.MaxPoolSize;
                var minPoolSize = builder.MinPoolSize;

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        COUNT(*) as CurrentConnections
                    FROM sys.dm_exec_sessions 
                    WHERE is_user_process = 1";

                using var command = new SqlCommand(query, connection);
                var currentConnections = Convert.ToInt32(await command.ExecuteScalarAsync());

                var status = new ConnectionPoolStatus
                {
                    MaxPoolSize = maxPoolSize,
                    MinPoolSize = minPoolSize,
                    CurrentActiveConnections = currentConnections,
                    CurrentIdleConnections = Math.Max(0, minPoolSize - currentConnections),
                    PoolUtilizationPercentage = (double)currentConnections / maxPoolSize * 100,
                    IsHealthy = currentConnections < maxPoolSize * 0.8 // %80'in altındaysa sağlıklı
                };

                return new SuccessDataResult<ConnectionPoolStatus>(status, "Connection pool durumu başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Connection pool durumu alınırken hata oluştu");
                return new ErrorDataResult<ConnectionPoolStatus>(null, "Connection pool durumu alınamadı.");
            }
        }

        public async Task<IDataResult<List<SlowQueryInfo>>> GetSlowQueriesAsync(int topCount = 10)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = $@"
                    SELECT TOP {topCount}
                        SUBSTRING(qt.text, (qs.statement_start_offset/2)+1, 
                            ((CASE qs.statement_end_offset
                                WHEN -1 THEN DATALENGTH(qt.text)
                                ELSE qs.statement_end_offset
                            END - qs.statement_start_offset)/2)+1) AS QueryText,
                        qs.total_elapsed_time / qs.execution_count / 1000.0 AS AverageExecutionTimeMs,
                        qs.execution_count AS ExecutionCount,
                        qs.total_worker_time / 1000.0 AS TotalCpuTimeMs,
                        DB_NAME() AS DatabaseName
                    FROM sys.dm_exec_query_stats qs
                    CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
                    WHERE qs.execution_count > 5
                    ORDER BY qs.total_elapsed_time / qs.execution_count DESC";

                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                var slowQueries = new List<SlowQueryInfo>();
                while (await reader.ReadAsync())
                {
                    slowQueries.Add(new SlowQueryInfo
                    {
                        QueryText = reader.GetString("QueryText").Trim(),
                        AverageExecutionTimeMs = reader.GetDouble("AverageExecutionTimeMs"),
                        ExecutionCount = reader.GetInt32("ExecutionCount"),
                        TotalCpuTimeMs = reader.GetDouble("TotalCpuTimeMs"),
                        DatabaseName = reader.GetString("DatabaseName")
                    });
                }

                return new SuccessDataResult<List<SlowQueryInfo>>(slowQueries, "Yavaş sorgular başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yavaş sorgular alınırken hata oluştu");
                return new ErrorDataResult<List<SlowQueryInfo>>(new List<SlowQueryInfo>(), "Yavaş sorgular alınamadı.");
            }
        }

        public async Task<IDataResult<DatabaseSizeInfo>> GetDatabaseSizeInfoAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        DB_NAME() AS DatabaseName,
                        SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS BIGINT) * 8.0 / 1024) AS UsedSpaceInMB,
                        SUM(size * 8.0 / 1024) AS SizeInMB
                    FROM sys.database_files
                    WHERE type = 0"; // Data files only

                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var sizeInfo = new DatabaseSizeInfo
                    {
                        DatabaseName = reader.GetString("DatabaseName"),
                        SizeInMB = reader.GetDouble("SizeInMB"),
                        UsedSpaceInMB = reader.GetDouble("UsedSpaceInMB"),
                        FreeSpaceInMB = reader.GetDouble("SizeInMB") - reader.GetDouble("UsedSpaceInMB"),
                        GrowthRatePercentage = 0 // Bu değer ayrı bir hesaplama gerektirir
                    };

                    return new SuccessDataResult<DatabaseSizeInfo>(sizeInfo, "Database boyut bilgisi başarıyla alındı.");
                }

                return new ErrorDataResult<DatabaseSizeInfo>(null, "Database boyut bilgisi alınamadı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database boyut bilgisi alınırken hata oluştu");
                return new ErrorDataResult<DatabaseSizeInfo>(null, "Database boyut bilgisi alınamadı.");
            }
        }

        public async Task<IDataResult<List<IndexFragmentationInfo>>> GetIndexFragmentationAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        OBJECT_NAME(ips.object_id) AS TableName,
                        i.name AS IndexName,
                        ips.avg_fragmentation_in_percent AS FragmentationPercentage,
                        ips.page_count AS PageCount,
                        CASE 
                            WHEN ips.avg_fragmentation_in_percent > 30 THEN 'REBUILD'
                            WHEN ips.avg_fragmentation_in_percent > 10 THEN 'REORGANIZE'
                            ELSE 'NO ACTION'
                        END AS RecommendedAction
                    FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
                    INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
                    WHERE ips.avg_fragmentation_in_percent > 10
                    AND i.name IS NOT NULL
                    AND ips.page_count > 100
                    ORDER BY ips.avg_fragmentation_in_percent DESC";

                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                var fragmentationInfo = new List<IndexFragmentationInfo>();
                while (await reader.ReadAsync())
                {
                    fragmentationInfo.Add(new IndexFragmentationInfo
                    {
                        TableName = reader.GetString("TableName"),
                        IndexName = reader.GetString("IndexName"),
                        FragmentationPercentage = reader.GetDouble("FragmentationPercentage"),
                        PageCount = reader.GetInt32("PageCount"),
                        RecommendedAction = reader.GetString("RecommendedAction")
                    });
                }

                return new SuccessDataResult<List<IndexFragmentationInfo>>(fragmentationInfo, "Index fragmentation bilgisi başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Index fragmentation bilgisi alınırken hata oluştu");
                return new ErrorDataResult<List<IndexFragmentationInfo>>(new List<IndexFragmentationInfo>(), "Index fragmentation bilgisi alınamadı.");
            }
        }

        public async Task<IDataResult<List<MissingIndexInfo>>> GetMissingIndexSuggestionsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT TOP 10
                        OBJECT_NAME(mid.object_id) AS TableName,
                        'IX_' + OBJECT_NAME(mid.object_id) + '_Missing_' + CAST(mig.index_group_handle AS VARCHAR(10)) AS SuggestedIndexName,
                        'CREATE INDEX [IX_' + OBJECT_NAME(mid.object_id) + '_Missing_' + CAST(mig.index_group_handle AS VARCHAR(10)) + '] ON ' + mid.statement + 
                        ' (' + ISNULL(mid.equality_columns,'') + 
                        CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END +
                        ISNULL(mid.inequality_columns, '') + ')' +
                        CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END AS CreateIndexStatement,
                        migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS ImprovementMeasure,
                        migs.user_seeks AS UserSeeks,
                        migs.user_scans AS UserScans
                    FROM sys.dm_db_missing_index_groups mig
                    INNER JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
                    INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
                    WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
                    AND mid.database_id = DB_ID()
                    ORDER BY ImprovementMeasure DESC";

                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                var missingIndexes = new List<MissingIndexInfo>();
                while (await reader.ReadAsync())
                {
                    missingIndexes.Add(new MissingIndexInfo
                    {
                        TableName = reader.GetString("TableName"),
                        SuggestedIndexName = reader.GetString("SuggestedIndexName"),
                        CreateIndexStatement = reader.GetString("CreateIndexStatement"),
                        ImprovementMeasure = reader.GetDouble("ImprovementMeasure"),
                        UserSeeks = reader.GetInt32("UserSeeks"),
                        UserScans = reader.GetInt32("UserScans")
                    });
                }

                return new SuccessDataResult<List<MissingIndexInfo>>(missingIndexes, "Missing index önerileri başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Missing index önerileri alınırken hata oluştu");
                return new ErrorDataResult<List<MissingIndexInfo>>(new List<MissingIndexInfo>(), "Missing index önerileri alınamadı.");
            }
        }

        public async Task<IDataResult<List<TenantPerformanceMetric>>> GetTenantPerformanceMetricsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        c.CompanyID AS TenantId,
                        c.CompanyName AS TenantName,
                        COUNT(DISTINCT m.MemberID) AS ActiveMemberCount,
                        0.0 AS AverageQueryTimeMs, -- Bu değer ayrı bir hesaplama gerektirir
                        COUNT(DISTINCT t.TransactionID) AS DailyTransactionCount,
                        0.0 AS CacheHitRatio, -- Bu değer cache service'den alınmalı
                        CASE WHEN COUNT(DISTINCT m.MemberID) < 10000 THEN 1 ELSE 0 END AS IsPerformanceHealthy
                    FROM Companies c
                    LEFT JOIN Members m ON c.CompanyID = m.CompanyID AND m.IsActive = 1
                    LEFT JOIN Transactions t ON c.CompanyID = t.CompanyID AND CAST(t.TransactionDate AS DATE) = CAST(GETDATE() AS DATE)
                    GROUP BY c.CompanyID, c.CompanyName
                    ORDER BY ActiveMemberCount DESC";

                using var command = new SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                var metrics = new List<TenantPerformanceMetric>();
                while (await reader.ReadAsync())
                {
                    metrics.Add(new TenantPerformanceMetric
                    {
                        TenantId = reader.GetInt32("TenantId"),
                        TenantName = reader.GetString("TenantName"),
                        ActiveMemberCount = reader.GetInt32("ActiveMemberCount"),
                        AverageQueryTimeMs = reader.GetDouble("AverageQueryTimeMs"),
                        DailyTransactionCount = reader.GetInt32("DailyTransactionCount"),
                        CacheHitRatio = reader.GetDouble("CacheHitRatio"),
                        IsPerformanceHealthy = reader.GetInt32("IsPerformanceHealthy") == 1
                    });
                }

                return new SuccessDataResult<List<TenantPerformanceMetric>>(metrics, "Tenant performans metrikleri başarıyla alındı.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tenant performans metrikleri alınırken hata oluştu");
                return new ErrorDataResult<List<TenantPerformanceMetric>>(new List<TenantPerformanceMetric>(), "Tenant performans metrikleri alınamadı.");
            }
        }
    }
}
