using Core.Utilities.Results;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Utilities.Monitoring
{
    /// <summary>
    /// Database performans izleme servisi interface'i
    /// 1000+ salon için kritik performans metrikleri
    /// </summary>
    public interface IDatabaseMonitoringService
    {
        /// <summary>
        /// Aktif database connection sayısını getirir
        /// </summary>
        Task<IDataResult<int>> GetActiveConnectionCountAsync();

        /// <summary>
        /// Connection pool durumunu getirir
        /// </summary>
        Task<IDataResult<ConnectionPoolStatus>> GetConnectionPoolStatusAsync();

        /// <summary>
        /// Yavaş çalışan sorguları getirir
        /// </summary>
        Task<IDataResult<List<SlowQueryInfo>>> GetSlowQueriesAsync(int topCount = 10);

        /// <summary>
        /// Database boyut bilgilerini getirir
        /// </summary>
        Task<IDataResult<DatabaseSizeInfo>> GetDatabaseSizeInfoAsync();

        /// <summary>
        /// Index fragmentation bilgilerini getirir
        /// </summary>
        Task<IDataResult<List<IndexFragmentationInfo>>> GetIndexFragmentationAsync();

        /// <summary>
        /// Missing index önerilerini getirir
        /// </summary>
        Task<IDataResult<List<MissingIndexInfo>>> GetMissingIndexSuggestionsAsync();

        /// <summary>
        /// Tenant bazlı performans metriklerini getirir
        /// </summary>
        Task<IDataResult<List<TenantPerformanceMetric>>> GetTenantPerformanceMetricsAsync();
    }

    public class ConnectionPoolStatus
    {
        public int MaxPoolSize { get; set; }
        public int MinPoolSize { get; set; }
        public int CurrentActiveConnections { get; set; }
        public int CurrentIdleConnections { get; set; }
        public double PoolUtilizationPercentage { get; set; }
        public bool IsHealthy { get; set; }
    }

    public class SlowQueryInfo
    {
        public string QueryText { get; set; }
        public double AverageExecutionTimeMs { get; set; }
        public int ExecutionCount { get; set; }
        public double TotalCpuTimeMs { get; set; }
        public string DatabaseName { get; set; }
    }

    public class DatabaseSizeInfo
    {
        public string DatabaseName { get; set; }
        public double SizeInMB { get; set; }
        public double UsedSpaceInMB { get; set; }
        public double FreeSpaceInMB { get; set; }
        public double GrowthRatePercentage { get; set; }
    }

    public class IndexFragmentationInfo
    {
        public string TableName { get; set; }
        public string IndexName { get; set; }
        public double FragmentationPercentage { get; set; }
        public string RecommendedAction { get; set; }
        public int PageCount { get; set; }
    }

    public class MissingIndexInfo
    {
        public string TableName { get; set; }
        public string SuggestedIndexName { get; set; }
        public string CreateIndexStatement { get; set; }
        public double ImprovementMeasure { get; set; }
        public int UserSeeks { get; set; }
        public int UserScans { get; set; }
    }

    public class TenantPerformanceMetric
    {
        public int TenantId { get; set; }
        public string TenantName { get; set; }
        public int ActiveMemberCount { get; set; }
        public double AverageQueryTimeMs { get; set; }
        public int DailyTransactionCount { get; set; }
        public double CacheHitRatio { get; set; }
        public bool IsPerformanceHealthy { get; set; }
    }
}
