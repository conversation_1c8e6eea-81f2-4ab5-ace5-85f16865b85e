using System;
using System.Threading.Tasks;
using Core.Utilities.Results;
using Microsoft.Extensions.Logging;

namespace Core.Utilities.Email
{
    /// <summary>
    /// Development ortamı için mock e-posta servisi
    /// Gerçek e-posta göndermez, sadece console'a log yazar
    /// </summary>
    public class MockEmailService : IEmailService
    {
        private readonly ILogger<MockEmailService> _logger;

        public MockEmailService(ILogger<MockEmailService> logger)
        {
            _logger = logger;
        }

        public async Task<IResult> SendPasswordResetEmailAsync(string toEmail, string userName, string resetToken, string resetUrl)
        {
            await Task.Delay(100); // Simulate async operation

            var message = $@"
=== MOCK EMAIL SERVICE ===
TO: {toEmail}
SUBJECT: Şifre Sıfırlama - GymKod Pro
USER: {userName}
RESET URL: {resetUrl}
TOKEN: {resetToken}
=========================";

            Console.WriteLine(message);
            _logger.LogInformation("Mock password reset email sent to {Email}", toEmail);

            return new SuccessResult("Mock e-posta gönderildi (development mode).");
        }

        public async Task<IResult> SendWelcomeEmailAsync(string toEmail, string userName, string tempPassword = null)
        {
            await Task.Delay(100); // Simulate async operation

            var passwordInfo = !string.IsNullOrEmpty(tempPassword) ? $"TEMP PASSWORD: {tempPassword}" : "No temp password";

            var message = $@"
=== MOCK EMAIL SERVICE ===
TO: {toEmail}
SUBJECT: Hoş Geldiniz - GymKod Pro
USER: {userName}
{passwordInfo}
=========================";

            Console.WriteLine(message);
            _logger.LogInformation("Mock welcome email sent to {Email}", toEmail);

            return new SuccessResult("Mock e-posta gönderildi (development mode).");
        }

        public async Task<IResult> SendEmailAsync(string toEmail, string subject, string htmlContent, string plainTextContent = null)
        {
            await Task.Delay(100); // Simulate async operation

            var message = $@"
=== MOCK EMAIL SERVICE ===
TO: {toEmail}
SUBJECT: {subject}
HTML CONTENT: {htmlContent?.Substring(0, Math.Min(100, htmlContent.Length ?? 0))}...
PLAIN CONTENT: {plainTextContent?.Substring(0, Math.Min(100, plainTextContent?.Length ?? 0))}...
=========================";

            Console.WriteLine(message);
            _logger.LogInformation("Mock email sent to {Email} with subject {Subject}", toEmail, subject);

            return new SuccessResult("Mock e-posta gönderildi (development mode).");
        }

        public async Task<IResult> SendTemplateEmailAsync(string toEmail, string templateId, object templateData)
        {
            await Task.Delay(100); // Simulate async operation

            var message = $@"
=== MOCK EMAIL SERVICE ===
TO: {toEmail}
TEMPLATE ID: {templateId}
TEMPLATE DATA: {System.Text.Json.JsonSerializer.Serialize(templateData)}
=========================";

            Console.WriteLine(message);
            _logger.LogInformation("Mock template email sent to {Email} with template {TemplateId}", toEmail, templateId);

            return new SuccessResult("Mock e-posta gönderildi (development mode).");
        }
    }
}
