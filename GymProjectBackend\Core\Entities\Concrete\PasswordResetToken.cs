using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Concrete
{
    /// <summary>
    /// Şifre sıfırlama token'ları için entity
    /// Multi-tenant yapıya uygun olarak tasarlandı
    /// </summary>
    public class PasswordResetToken : IEntity
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Token'ın ait olduğu kullanıcı ID'si
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Şifre sıfırlama token'ı (GUID formatında)
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// Token'ın oluşturulma tarihi
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Token'ın son kullanma tarihi (varsayılan: 1 saat)
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// Token'ın kullanılıp kullanılmadığı
        /// </summary>
        public bool IsUsed { get; set; }

        /// <summary>
        /// Token'ın kullanıldığı tarih (opsiyonel)
        /// </summary>
        public DateTime? UsedDate { get; set; }

        /// <summary>
        /// İsteği yapan IP adresi (güvenlik için)
        /// </summary>
        public string RequestIpAddress { get; set; }

        /// <summary>
        /// Token'ın kullanıldığı IP adresi (güvenlik için)
        /// </summary>
        public string UsedIpAddress { get; set; }

        /// <summary>
        /// Token'ın geçerli olup olmadığını kontrol eder
        /// </summary>
        public bool IsValid()
        {
            return !IsUsed && DateTime.Now <= ExpiryDate;
        }

        /// <summary>
        /// Token'ı kullanıldı olarak işaretler
        /// </summary>
        /// <param name="usedIpAddress">Token'ın kullanıldığı IP adresi</param>
        public void MarkAsUsed(string usedIpAddress = null)
        {
            IsUsed = true;
            UsedDate = DateTime.Now;
            UsedIpAddress = usedIpAddress;
        }
    }
}
