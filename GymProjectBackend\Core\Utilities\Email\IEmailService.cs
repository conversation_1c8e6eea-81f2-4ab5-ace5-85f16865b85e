using System.Threading.Tasks;
using Core.Utilities.Results;

namespace Core.Utilities.Email
{
    /// <summary>
    /// E-posta gönderme servisi interface'i
    /// SOLID prensiplerine uygun olarak abstraction sağlar
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Şifre sıfırlama e-postası gönderir
        /// </summary>
        /// <param name="toEmail">Alıcı e-posta adresi</param>
        /// <param name="userName">Kullanıcı adı</param>
        /// <param name="resetToken"><PERSON><PERSON>re sıfırlama token'ı</param>
        /// <param name="resetUrl">Ş<PERSON>re sıfırlama URL'i</param>
        /// <returns>İşlem sonucu</returns>
        Task<IResult> SendPasswordResetEmailAsync(string toEmail, string userName, string resetToken, string resetUrl);

        /// <summary>
        /// Ho<PERSON> geldin e-postası gönderir
        /// </summary>
        /// <param name="toEmail">Alıcı e-posta adresi</param>
        /// <param name="userName"><PERSON>llanı<PERSON>ı adı</param>
        /// <param name="tempPassword">Geçici şifre (opsiyonel)</param>
        /// <returns>İşlem sonucu</returns>
        Task<IResult> SendWelcomeEmailAsync(string toEmail, string userName, string tempPassword = null);

        /// <summary>
        /// Genel e-posta gönderme metodu
        /// </summary>
        /// <param name="toEmail">Alıcı e-posta adresi</param>
        /// <param name="subject">E-posta konusu</param>
        /// <param name="htmlContent">HTML içerik</param>
        /// <param name="plainTextContent">Düz metin içerik (opsiyonel)</param>
        /// <returns>İşlem sonucu</returns>
        Task<IResult> SendEmailAsync(string toEmail, string subject, string htmlContent, string plainTextContent = null);

        /// <summary>
        /// SendGrid template kullanarak e-posta gönderir
        /// </summary>
        /// <param name="toEmail">Alıcı e-posta adresi</param>
        /// <param name="templateId">SendGrid template ID'si</param>
        /// <param name="templateData">Template için dinamik veriler</param>
        /// <returns>İşlem sonucu</returns>
        Task<IResult> SendTemplateEmailAsync(string toEmail, string templateId, object templateData);
    }
}
