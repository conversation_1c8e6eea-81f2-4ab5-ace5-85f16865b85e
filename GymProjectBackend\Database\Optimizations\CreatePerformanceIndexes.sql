-- 1000+ Salon için Database Performance Optimizasyonları
-- Bu script'i production'da çalıştırmadan önce backup alın!

-- =====================================================
-- MEMBERS TABLOSU İNDEXLERİ (En kritik tablo)
-- =====================================================

-- CompanyID bazlı sorgular için (Multi-tenant en önemli index)
CREATE NONCLUSTERED INDEX [IX_Members_CompanyID_IsActive] 
ON [dbo].[Members] ([CompanyID] ASC, [IsActive] ASC)
INCLUDE ([MemberID], [FirstName], [LastName], [PhoneNumber], [Email])

-- Telefon numarası aramaları için (Çok sık kullanılır)
CREATE NONCLUSTERED INDEX [IX_Members_PhoneNumber_CompanyID] 
ON [dbo].[Members] ([PhoneNumber] ASC, [CompanyID] ASC)
WHERE [IsActive] = 1

-- E-posta aramaları için
CREATE NONCLUSTERED INDEX [IX_Members_Email_CompanyID] 
ON [dbo].[Members] ([Email] ASC, [CompanyID] ASC)
WHERE [IsActive] = 1 AND [Email] IS NOT NULL

-- QR kod taramaları için (Giriş-çıkış sistemi)
CREATE NONCLUSTERED INDEX [IX_Members_ScanNumber_CompanyID] 
ON [dbo].[Members] ([ScanNumber] ASC, [CompanyID] ASC)
WHERE [IsActive] = 1

-- Doğum günü sorguları için
CREATE NONCLUSTERED INDEX [IX_Members_BirthDate_CompanyID] 
ON [dbo].[Members] ([BirthDate] ASC, [CompanyID] ASC)
WHERE [IsActive] = 1 AND [BirthDate] IS NOT NULL

-- =====================================================
-- MEMBERSHIPS TABLOSU İNDEXLERİ
-- =====================================================

-- Aktif üyelikler için (En sık kullanılan sorgu)
CREATE NONCLUSTERED INDEX [IX_Memberships_MemberID_IsActive] 
ON [dbo].[Memberships] ([MemberID] ASC, [IsActive] ASC)
INCLUDE ([StartDate], [EndDate], [MembershipTypeID])

-- Bitiş tarihi sorguları için (Süresi dolan üyelikler)
CREATE NONCLUSTERED INDEX [IX_Memberships_EndDate_IsActive] 
ON [dbo].[Memberships] ([EndDate] ASC, [IsActive] ASC)
INCLUDE ([MemberID], [MembershipTypeID])

-- Başlangıç tarihi sorguları için
CREATE NONCLUSTERED INDEX [IX_Memberships_StartDate_IsActive] 
ON [dbo].[Memberships] ([StartDate] ASC, [IsActive] ASC)

-- =====================================================
-- PAYMENTS TABLOSU İNDEXLERİ
-- =====================================================

-- Ödeme geçmişi sorguları için (Pagination ile)
CREATE NONCLUSTERED INDEX [IX_Payments_MemberID_PaymentDate] 
ON [dbo].[Payments] ([MemberID] ASC, [PaymentDate] DESC)
INCLUDE ([Amount], [PaymentMethod], [Description])

-- Günlük/aylık raporlar için
CREATE NONCLUSTERED INDEX [IX_Payments_PaymentDate_CompanyID] 
ON [dbo].[Payments] ([PaymentDate] DESC, [CompanyID] ASC)
INCLUDE ([Amount], [PaymentMethod])

-- =====================================================
-- ENTRY_EXIT_HISTORY TABLOSU İNDEXLERİ
-- =====================================================

-- Günlük giriş-çıkış raporları için
CREATE NONCLUSTERED INDEX [IX_EntryExitHistory_EntryDate_CompanyID] 
ON [dbo].[EntryExitHistories] ([EntryDate] DESC, [CompanyID] ASC)
INCLUDE ([MemberID], [ExitDate])

-- Üye bazlı giriş-çıkış geçmişi için
CREATE NONCLUSTERED INDEX [IX_EntryExitHistory_MemberID_EntryDate] 
ON [dbo].[EntryExitHistories] ([MemberID] ASC, [EntryDate] DESC)

-- =====================================================
-- USERS TABLOSU İNDEXLERİ
-- =====================================================

-- E-posta ile giriş için
CREATE NONCLUSTERED INDEX [IX_Users_Email_IsActive] 
ON [dbo].[Users] ([Email] ASC, [IsActive] ASC)
INCLUDE ([UserID], [FirstName], [LastName], [PasswordHash], [PasswordSalt])

-- =====================================================
-- USER_COMPANIES TABLOSU İNDEXLERİ
-- =====================================================

-- Kullanıcının şirketleri için
CREATE NONCLUSTERED INDEX [IX_UserCompanies_UserID] 
ON [dbo].[UserCompanies] ([UserID] ASC)
INCLUDE ([CompanyID])

-- Şirketin kullanıcıları için
CREATE NONCLUSTERED INDEX [IX_UserCompanies_CompanyID] 
ON [dbo].[UserCompanies] ([CompanyID] ASC)
INCLUDE ([UserID])

-- =====================================================
-- TRANSACTIONS TABLOSU İNDEXLERİ
-- =====================================================

-- Günlük işlem raporları için
CREATE NONCLUSTERED INDEX [IX_Transactions_TransactionDate_CompanyID] 
ON [dbo].[Transactions] ([TransactionDate] DESC, [CompanyID] ASC)
INCLUDE ([Amount], [TransactionType], [Description])

-- Üye bazlı işlemler için
CREATE NONCLUSTERED INDEX [IX_Transactions_MemberID_TransactionDate] 
ON [dbo].[Transactions] ([MemberID] ASC, [TransactionDate] DESC)
WHERE [MemberID] IS NOT NULL

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Missing index queries için view
CREATE VIEW [dbo].[MissingIndexes] AS
SELECT 
    migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure,
    'CREATE INDEX [IX_' + OBJECT_NAME(mid.object_id) + '_' + REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns,''), ', ', '_'), '[', ''), ']', '') + 
    CASE WHEN mid.inequality_columns IS NOT NULL THEN '_' + REPLACE(REPLACE(REPLACE(mid.inequality_columns, ', ', '_'), '[', ''), ']', '') ELSE '' END + ']' +
    ' ON ' + mid.statement + ' (' + ISNULL (mid.equality_columns,'') +
    CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END +
    CASE WHEN mid.inequality_columns IS NOT NULL THEN mid.inequality_columns ELSE '' END + ')' +
    CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END AS create_index_statement,
    migs.*,
    mid.database_id,
    mid.[object_id]
FROM sys.dm_db_missing_index_groups mig
INNER JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
AND mid.database_id = DB_ID()

-- =====================================================
-- INDEX MAINTENANCE JOB SCRIPT
-- =====================================================

-- Bu script'i SQL Server Agent Job olarak haftalık çalıştırın
/*
DECLARE @sql NVARCHAR(MAX) = ''
SELECT @sql = @sql + 
    CASE 
        WHEN avg_fragmentation_in_percent > 30 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(i.object_id) + '].[' + OBJECT_NAME(i.object_id) + '] REBUILD;' + CHAR(13)
        WHEN avg_fragmentation_in_percent > 10 THEN 'ALTER INDEX [' + i.name + '] ON [' + OBJECT_SCHEMA_NAME(i.object_id) + '].[' + OBJECT_NAME(i.object_id) + '] REORGANIZE;' + CHAR(13)
        ELSE ''
    END
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10
AND i.name IS NOT NULL

EXEC sp_executesql @sql
*/
