/* Mevcut auth component'lerin stilini takip eder */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bs-body-bg);
  padding: 20px;
}

.auth-card {
  background: var(--bs-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 12px;
  box-shadow: var(--bs-box-shadow);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h2 {
  color: var(--bs-heading-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.auth-header p {
  color: var(--bs-secondary-color);
  font-size: 0.9rem;
  margin: 0;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--bs-body-color);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  background-color: var(--bs-body-bg);
  color: var(--bs-body-color);
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--bs-primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-control.is-invalid {
  border-color: var(--bs-danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--bs-danger);
}

.form-actions {
  margin-bottom: 1.5rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--bs-primary-hover, #0056b3);
  border-color: var(--bs-primary-hover, #0056b3);
}

.btn-primary:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-block {
  display: block;
  width: 100%;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.auth-footer {
  text-align: center;
  margin-top: 1rem;
}

.auth-footer p {
  color: var(--bs-secondary-color);
  font-size: 0.9rem;
  margin: 0;
}

.auth-link {
  color: var(--bs-primary);
  text-decoration: none;
  font-weight: 500;
}

.auth-link:hover {
  color: var(--bs-primary-hover, #0056b3);
  text-decoration: underline;
}

/* Dark mode uyumluluğu */
[data-bs-theme="dark"] .auth-card {
  background: var(--bs-dark);
  border-color: var(--bs-border-color-translucent);
}

[data-bs-theme="dark"] .form-control {
  background-color: var(--bs-dark);
  border-color: var(--bs-border-color-translucent);
  color: var(--bs-light);
}

[data-bs-theme="dark"] .form-control:focus {
  background-color: var(--bs-dark);
  border-color: var(--bs-primary);
  color: var(--bs-light);
}
