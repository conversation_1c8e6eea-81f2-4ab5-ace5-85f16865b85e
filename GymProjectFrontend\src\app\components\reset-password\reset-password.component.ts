import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-reset-password',
  standalone: false,
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css']
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm!: FormGroup;
  isLoading = false;
  isSubmitted = false;
  isValidatingToken = true;
  isTokenValid = false;
  token: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.createResetPasswordForm();
    this.getTokenFromUrl();
  }

  createResetPasswordForm(): void {
    this.resetPasswordForm = this.formBuilder.group({
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  getTokenFromUrl(): void {
    this.route.queryParams.subscribe(params => {
      this.token = params['token'];
      if (this.token) {
        this.validateToken();
      } else {
        this.isValidatingToken = false;
        this.isTokenValid = false;
        this.toastrService.error('Geçersiz şifre sıfırlama bağlantısı');
      }
    });
  }

  validateToken(): void {
    this.authService.validateResetToken(this.token)
      .pipe(finalize(() => this.isValidatingToken = false))
      .subscribe({
        next: (response) => {
          this.isTokenValid = response.success;
          if (!response.success) {
            this.toastrService.error(response.message || 'Geçersiz veya süresi dolmuş token');
          }
        },
        error: (error) => {
          this.isTokenValid = false;
          this.toastrService.error(error.message || 'Token doğrulama sırasında hata oluştu');
        }
      });
  }

  get f() {
    return this.resetPasswordForm.controls;
  }

  onSubmit(): void {
    this.isSubmitted = true;

    if (this.resetPasswordForm.invalid) {
      return;
    }

    const newPassword = this.resetPasswordForm.get('newPassword')?.value;

    this.isLoading = true;
    this.authService.resetPassword(this.token, newPassword)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message || 'Şifreniz başarıyla değiştirildi');
            this.router.navigate(['/login']);
          } else {
            this.toastrService.error(response.message || 'Şifre sıfırlama işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Şifre sıfırlama işlemi sırasında bir hata oluştu');
        }
      });
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  goToForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }
}
