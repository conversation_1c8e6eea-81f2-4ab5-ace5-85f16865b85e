using Core.Utilities.Results;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Şifre sıfırlama işlemleri için business service interface'i
    /// SOLID prensiplerine uygun olarak tasarlandı
    /// </summary>
    public interface IPasswordResetService
    {
        /// <summary>
        /// E-posta adresine şifre sıfırlama bağlantısı gönderir
        /// </summary>
        /// <param name="email">Kullanıcının e-posta adresi</param>
        /// <param name="requestIpAddress">İsteği yapan IP adresi (güvenlik için)</param>
        /// <returns>İşlem sonucu</returns>
        Task<IResult> SendPasswordResetEmailAsync(string email, string requestIpAddress = null);

        /// <summary>
        /// Şifre sıfırlama token'ını doğrular
        /// </summary>
        /// <param name="token">Şifre sıfırlama token'ı</param>
        /// <returns>Token geçerliliği sonucu</returns>
        IResult ValidatePasswordResetToken(string token);

        /// <summary>
        /// Token kullanarak şifreyi sıfırlar
        /// </summary>
        /// <param name="token">Şifre sıfırlama token'ı</param>
        /// <param name="newPassword">Yeni şifre</param>
        /// <param name="usedIpAddress">Token'ın kullanıldığı IP adresi</param>
        /// <returns>İşlem sonucu</returns>
        IResult ResetPasswordWithToken(string token, string newPassword, string usedIpAddress = null);

        /// <summary>
        /// Kullanıcının aktif şifre sıfırlama token'larını iptal eder
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="usedIpAddress">İşlemi yapan IP adresi</param>
        /// <returns>İşlem sonucu</returns>
        IResult CancelUserPasswordResetTokens(int userId, string usedIpAddress = null);

        /// <summary>
        /// Süresi dolmuş token'ları temizler (maintenance işlemi)
        /// </summary>
        /// <param name="olderThanDays">Kaç gün önceki token'ları temizleyeceği</param>
        /// <returns>Temizlenen token sayısı</returns>
        IDataResult<int> CleanupExpiredTokens(int olderThanDays = 7);
    }
}
