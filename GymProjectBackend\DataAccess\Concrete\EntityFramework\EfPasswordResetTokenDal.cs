using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Şifre sıfırlama token'ları için Entity Framework Data Access Layer implementation
    /// SOLID prensiplerine uygun olarak tasarlandı
    /// 1000+ salon için optimize edilmiş
    /// </summary>
    public class EfPasswordResetTokenDal : EfEntityRepositoryBase<PasswordResetToken, GymContext>, IPasswordResetTokenDal
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public EfPasswordResetTokenDal(GymContext context) : base(context)
        {
            _context = context;
        }

        public PasswordResetToken GetValidTokenByTokenString(string token)
        {
            if (string.IsNullOrEmpty(token))
                return null;

            try
            {
                // Geçerli token'ı bul: kullanılmamış ve süresi dolmamış
                return _context.PasswordResetTokens
                    .FirstOrDefault(t => t.Token == token && 
                                   !t.IsUsed && 
                                   t.ExpiryDate > DateTime.Now);
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"GetValidTokenByTokenString Error: {ex.Message}");
                return null;
            }
        }

        public List<PasswordResetToken> GetActiveTokensByUserId(int userId)
        {
            if (userId <= 0)
                return new List<PasswordResetToken>();

            try
            {
                // Kullanıcının aktif token'larını getir
                return _context.PasswordResetTokens
                    .Where(t => t.UserId == userId && 
                              !t.IsUsed && 
                              t.ExpiryDate > DateTime.Now)
                    .OrderByDescending(t => t.CreatedDate)
                    .ToList();
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"GetActiveTokensByUserId Error: {ex.Message}");
                return new List<PasswordResetToken>();
            }
        }

        public void DeactivateAllUserTokens(int userId, string usedIpAddress = null)
        {
            if (userId <= 0)
                return;

            try
            {
                // Kullanıcının tüm aktif token'larını bul
                var activeTokens = _context.PasswordResetTokens
                    .Where(t => t.UserId == userId && !t.IsUsed)
                    .ToList();

                // Tüm aktif token'ları pasif hale getir
                foreach (var token in activeTokens)
                {
                    token.MarkAsUsed(usedIpAddress);
                }

                // Değişiklikleri kaydet
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"DeactivateAllUserTokens Error: {ex.Message}");
                throw; // Bu durumda exception'ı yukarı fırlat çünkü kritik bir işlem
            }
        }

        public int CleanupExpiredTokens(int olderThanDays = 7)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-olderThanDays);
                
                // Süresi dolmuş ve eski token'ları bul
                var expiredTokens = _context.PasswordResetTokens
                    .Where(t => (t.ExpiryDate < DateTime.Now || t.IsUsed) && 
                              t.CreatedDate < cutoffDate)
                    .ToList();

                if (expiredTokens.Any())
                {
                    _context.PasswordResetTokens.RemoveRange(expiredTokens);
                    _context.SaveChanges();
                }

                return expiredTokens.Count;
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"CleanupExpiredTokens Error: {ex.Message}");
                return 0;
            }
        }

        public int CountTokenRequestsByIp(string ipAddress, DateTime sinceDate)
        {
            if (string.IsNullOrEmpty(ipAddress))
                return 0;

            try
            {
                // Belirli IP adresinden belirli tarihten sonra yapılan token isteklerini say
                return _context.PasswordResetTokens
                    .Count(t => t.RequestIpAddress == ipAddress && 
                              t.CreatedDate >= sinceDate);
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"CountTokenRequestsByIp Error: {ex.Message}");
                return 0;
            }
        }
    }
}
