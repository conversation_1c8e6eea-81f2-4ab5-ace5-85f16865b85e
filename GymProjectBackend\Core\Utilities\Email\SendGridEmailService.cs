using System;
using System.Threading.Tasks;
using SendGrid;
using SendGrid.Helpers.Mail;
using Core.Utilities.Results;
using Microsoft.Extensions.Logging;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;

namespace Core.Utilities.Email
{
    /// <summary>
    /// SendGrid kullanarak e-posta gönderme servisi
    /// SOLID prensiplerine uygun implementation
    /// 1000+ salon için optimize edilmiş
    /// </summary>
    public class SendGridEmailService : IEmailService
    {
        private readonly ISendGridClient _sendGridClient;
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<SendGridEmailService> _logger;

        public SendGridEmailService(
            ISendGridClient sendGridClient, 
            EmailSettings emailSettings,
            ILogger<SendGridEmailService> logger)
        {
            _sendGridClient = sendGridClient ?? throw new ArgumentNullException(nameof(sendGridClient));
            _emailSettings = emailSettings ?? throw new ArgumentNullException(nameof(emailSettings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // E-posta ayarlarının geçerliliğini kontrol et
            if (!_emailSettings.IsValid())
            {
                throw new InvalidOperationException("E-posta ayarları geçersiz. SendGridApiKey, FromEmail ve FromName alanları zorunludur.");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendPasswordResetEmailAsync(string toEmail, string userName, string resetToken, string resetUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(toEmail) || string.IsNullOrEmpty(userName) || 
                    string.IsNullOrEmpty(resetToken) || string.IsNullOrEmpty(resetUrl))
                {
                    return new ErrorResult("E-posta gönderimi için gerekli parametreler eksik.");
                }

                // Template kullanarak e-posta gönder
                if (!string.IsNullOrEmpty(_emailSettings.PasswordResetTemplateId))
                {
                    var templateData = new
                    {
                        user_name = userName,
                        reset_url = resetUrl,
                        reset_token = resetToken,
                        company_name = "GymKod Pro"
                    };

                    return await SendTemplateEmailAsync(toEmail, _emailSettings.PasswordResetTemplateId, templateData);
                }

                // Template yoksa HTML e-posta gönder
                var subject = "Şifre Sıfırlama - GymKod Pro";
                var htmlContent = GeneratePasswordResetHtml(userName, resetUrl);
                var plainTextContent = $"Merhaba {userName},\n\nŞifrenizi sıfırlamak için aşağıdaki bağlantıya tıklayın:\n{resetUrl}\n\nBu bağlantı 1 saat geçerlidir.\n\nGymKod Pro Ekibi";

                return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Şifre sıfırlama e-postası gönderilirken hata oluştu. Email: {Email}", toEmail);
                return new ErrorResult("Şifre sıfırlama e-postası gönderilemedi. Lütfen daha sonra tekrar deneyin.");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendWelcomeEmailAsync(string toEmail, string userName, string tempPassword = null)
        {
            try
            {
                if (string.IsNullOrEmpty(toEmail) || string.IsNullOrEmpty(userName))
                {
                    return new ErrorResult("E-posta gönderimi için gerekli parametreler eksik.");
                }

                // Template kullanarak e-posta gönder
                if (!string.IsNullOrEmpty(_emailSettings.WelcomeTemplateId))
                {
                    var templateData = new
                    {
                        user_name = userName,
                        temp_password = tempPassword,
                        company_name = "GymKod Pro",
                        has_temp_password = !string.IsNullOrEmpty(tempPassword)
                    };

                    return await SendTemplateEmailAsync(toEmail, _emailSettings.WelcomeTemplateId, templateData);
                }

                // Template yoksa HTML e-posta gönder
                var subject = "Hoş Geldiniz - GymKod Pro";
                var htmlContent = GenerateWelcomeHtml(userName, tempPassword);
                var plainTextContent = GenerateWelcomePlainText(userName, tempPassword);

                return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hoş geldin e-postası gönderilirken hata oluştu. Email: {Email}", toEmail);
                return new ErrorResult("Hoş geldin e-postası gönderilemedi.");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendEmailAsync(string toEmail, string subject, string htmlContent, string plainTextContent = null)
        {
            try
            {
                var from = new EmailAddress(_emailSettings.FromEmail, _emailSettings.FromName);
                var to = new EmailAddress(toEmail);
                
                var msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);
                
                var response = await _sendGridClient.SendEmailAsync(msg);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("E-posta başarıyla gönderildi. Email: {Email}, Subject: {Subject}", toEmail, subject);
                    return new SuccessResult("E-posta başarıyla gönderildi.");
                }
                else
                {
                    var responseBody = await response.Body.ReadAsStringAsync();
                    _logger.LogWarning("E-posta gönderilemedi. StatusCode: {StatusCode}, Response: {Response}", 
                        response.StatusCode, responseBody);
                    return new ErrorResult("E-posta gönderilemedi. Lütfen daha sonra tekrar deneyin.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "E-posta gönderilirken hata oluştu. Email: {Email}", toEmail);
                return new ErrorResult("E-posta gönderilemedi. Sistem hatası oluştu.");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendTemplateEmailAsync(string toEmail, string templateId, object templateData)
        {
            try
            {
                var from = new EmailAddress(_emailSettings.FromEmail, _emailSettings.FromName);
                var to = new EmailAddress(toEmail);
                
                var msg = MailHelper.CreateSingleTemplateEmail(from, to, templateId, templateData);
                
                var response = await _sendGridClient.SendEmailAsync(msg);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Template e-posta başarıyla gönderildi. Email: {Email}, TemplateId: {TemplateId}", 
                        toEmail, templateId);
                    return new SuccessResult("E-posta başarıyla gönderildi.");
                }
                else
                {
                    var responseBody = await response.Body.ReadAsStringAsync();
                    _logger.LogWarning("Template e-posta gönderilemedi. StatusCode: {StatusCode}, Response: {Response}", 
                        response.StatusCode, responseBody);
                    return new ErrorResult("E-posta gönderilemedi. Lütfen daha sonra tekrar deneyin.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Template e-posta gönderilirken hata oluştu. Email: {Email}, TemplateId: {TemplateId}", 
                    toEmail, templateId);
                return new ErrorResult("E-posta gönderilemedi. Sistem hatası oluştu.");
            }
        }

        private string GeneratePasswordResetHtml(string userName, string resetUrl)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Şifre Sıfırlama</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <h2 style='color: #2c3e50;'>Şifre Sıfırlama</h2>
        <p>Merhaba <strong>{userName}</strong>,</p>
        <p>Şifrenizi sıfırlamak için aşağıdaki butona tıklayın:</p>
        <div style='text-align: center; margin: 30px 0;'>
            <a href='{resetUrl}' style='background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Şifremi Sıfırla</a>
        </div>
        <p><strong>Önemli:</strong> Bu bağlantı 1 saat geçerlidir.</p>
        <p>Eğer bu işlemi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.</p>
        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
        <p style='font-size: 12px; color: #666;'>GymKod Pro Ekibi</p>
    </div>
</body>
</html>";
        }

        private string GenerateWelcomeHtml(string userName, string tempPassword)
        {
            var passwordSection = !string.IsNullOrEmpty(tempPassword) 
                ? $"<p><strong>Geçici Şifreniz:</strong> {tempPassword}</p><p>Güvenliğiniz için ilk girişinizde şifrenizi değiştirmenizi öneririz.</p>"
                : "";

            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Hoş Geldiniz</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <h2 style='color: #2c3e50;'>GymKod Pro'ya Hoş Geldiniz!</h2>
        <p>Merhaba <strong>{userName}</strong>,</p>
        <p>GymKod Pro ailesine katıldığınız için teşekkür ederiz.</p>
        {passwordSection}
        <p>Herhangi bir sorunuz olursa bizimle iletişime geçmekten çekinmeyin.</p>
        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
        <p style='font-size: 12px; color: #666;'>GymKod Pro Ekibi</p>
    </div>
</body>
</html>";
        }

        private string GenerateWelcomePlainText(string userName, string tempPassword)
        {
            var passwordSection = !string.IsNullOrEmpty(tempPassword) 
                ? $"\n\nGeçici Şifreniz: {tempPassword}\nGüvenliğiniz için ilk girişinizde şifrenizi değiştirmenizi öneririz."
                : "";

            return $"Merhaba {userName},\n\nGymKod Pro ailesine katıldığınız için teşekkür ederiz.{passwordSection}\n\nHerhangi bir sorunuz olursa bizimle iletişime geçmekten çekinmeyin.\n\nGymKod Pro Ekibi";
        }
    }
}
