<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Şifremi Unuttum</h2>
      <p>E-posta adresinizi girin, size şifre sıfırlama bağlantısı gönderelim</p>
    </div>

    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-group">
        <label for="email">E-posta Adresi</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.is-invalid]="isSubmitted && f['email'].errors"
          placeholder="E-posta adresinizi girin"
        />
        <div *ngIf="isSubmitted && f['email'].errors" class="invalid-feedback">
          <div *ngIf="f['email'].errors?.['required']">E-posta adresi gereklidir</div>
          <div *ngIf="f['email'].errors?.['email']">Geçerli bir e-posta adresi girin</div>
        </div>
      </div>

      <div class="form-actions">
        <button
          type="submit"
          class="btn btn-primary btn-block"
          [disabled]="isLoading"
        >
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
          {{ isLoading ? 'Gönderiliyor...' : 'Şifre Sıfırlama Bağlantısı Gönder' }}
        </button>
      </div>

      <div class="auth-footer">
        <p>
          Şifrenizi hatırladınız mı?
          <a href="javascript:void(0)" (click)="goToLogin()" class="auth-link">Giriş Yap</a>
        </p>
      </div>
    </form>
  </div>
</div>
