-- <PERSON><PERSON><PERSON><PERSON>rlama token'ları için tablo oluşturma
-- Multi-tenant yapıya uygun olarak tasarlandı
-- 1000+ salon için optimize edilmiş indexler

CREATE TABLE [dbo].[PasswordResetTokens](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [Token] [nvarchar](255) NOT NULL,
    [CreatedDate] [datetime2](7) NOT NULL,
    [ExpiryDate] [datetime2](7) NOT NULL,
    [IsUsed] [bit] NOT NULL DEFAULT 0,
    [UsedDate] [datetime2](7) NULL,
    [RequestIpAddress] [nvarchar](45) NULL,
    [UsedIpAddress] [nvarchar](45) NULL,
    
    CONSTRAINT [PK_PasswordResetTokens] PRIMARY KEY CLUSTERED ([Id] ASC),
    
    -- Foreign key constraint
    CONSTRAINT [FK_PasswordResetTokens_Users] FOREIGN KEY([UserId]) 
        REFERENCES [dbo].[Users] ([UserID]) ON DELETE CASCADE
) ON [PRIMARY]

-- Performance için indexler (1000+ salon optimizasyonu)
CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_Token] 
    ON [dbo].[PasswordResetTokens] ([Token] ASC)

CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_UserId_IsUsed] 
    ON [dbo].[PasswordResetTokens] ([UserId] ASC, [IsUsed] ASC)

CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_ExpiryDate_IsUsed] 
    ON [dbo].[PasswordResetTokens] ([ExpiryDate] ASC, [IsUsed] ASC)

-- Otomatik temizlik için expired token'ları silme (opsiyonel)
-- Bu script'i SQL Server Agent Job olarak çalıştırabilirsiniz
/*
-- Günlük temizlik job'ı için script
DELETE FROM [dbo].[PasswordResetTokens] 
WHERE [ExpiryDate] < DATEADD(day, -7, GETDATE()) -- 7 gün önceki expired token'ları sil

-- Alternatif: Sadece kullanılmış ve expired token'ları sil
DELETE FROM [dbo].[PasswordResetTokens] 
WHERE ([IsUsed] = 1 OR [ExpiryDate] < GETDATE()) 
  AND [CreatedDate] < DATEADD(day, -30, GETDATE()) -- 30 gün önceki kayıtları sil
*/
