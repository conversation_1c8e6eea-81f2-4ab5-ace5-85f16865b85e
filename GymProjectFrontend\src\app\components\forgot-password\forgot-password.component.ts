import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-forgot-password',
  standalone: false,
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm!: FormGroup;
  isLoading = false;
  isSubmitted = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createForgotPasswordForm();
  }

  createForgotPasswordForm(): void {
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  get f() {
    return this.forgotPasswordForm.controls;
  }

  onSubmit(): void {
    this.isSubmitted = true;

    if (this.forgotPasswordForm.invalid) {
      return;
    }

    const email = this.forgotPasswordForm.get('email')?.value;

    this.isLoading = true;
    this.authService.forgotPassword(email)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message || 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi');
            this.router.navigate(['/login']);
          } else {
            this.toastrService.error(response.message || 'Şifre sıfırlama işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Şifre sıfırlama işlemi sırasında bir hata oluştu');
        }
      });
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }
}
