using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Utilities.Email;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace Business.Concrete
{
    /// <summary>
    /// Şifre sıfırlama işlemleri için business service implementation
    /// SOLID prensiplerine uygun olarak tasarlandı
    /// 1000+ salon için optimize edilmiş
    /// </summary>
    public class PasswordResetManager : IPasswordResetService
    {
        private readonly IPasswordResetTokenDal _passwordResetTokenDal;
        private readonly IUserService _userService;
        private readonly IEmailService _emailService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConfiguration _configuration;

        // Rate limiting için sabitler
        private const int MAX_REQUESTS_PER_HOUR = 5; // Saatte maksimum 5 şifre sıfırlama isteği
        private const int TOKEN_EXPIRY_HOURS = 1; // Token 1 saat geçerli

        public PasswordResetManager(
            IPasswordResetTokenDal passwordResetTokenDal,
            IUserService userService,
            IEmailService emailService,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration)
        {
            _passwordResetTokenDal = passwordResetTokenDal;
            _userService = userService;
            _emailService = emailService;
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
        }

        [LogAspect]
        [PerformanceAspect(5)]
        [TransactionScopeAspect]
        public async Task<IResult> SendPasswordResetEmailAsync(string email, string requestIpAddress = null)
        {
            try
            {
                // E-posta adresini doğrula
                if (string.IsNullOrEmpty(email))
                {
                    return new ErrorResult("E-posta adresi boş olamaz.");
                }

                // IP adresini al (güvenlik için)
                if (string.IsNullOrEmpty(requestIpAddress))
                {
                    requestIpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
                }

                // Rate limiting kontrolü
                var rateLimit = CheckRateLimit(requestIpAddress);
                if (!rateLimit.Success)
                {
                    return rateLimit;
                }

                // Kullanıcıyı bul
                var user = _userService.GetByMail(email);
                if (user == null)
                {
                    // Güvenlik için: Kullanıcı bulunamasa bile başarılı mesajı döndür
                    // Bu sayede e-posta adresinin sistemde olup olmadığı anlaşılamaz
                    return new SuccessResult("Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama bağlantısı gönderilmiştir.");
                }

                // Kullanıcının aktif token'larını iptal et
                _passwordResetTokenDal.DeactivateAllUserTokens(user.UserID, requestIpAddress);

                // Yeni token oluştur
                var token = GenerateSecureToken();
                var passwordResetToken = new PasswordResetToken
                {
                    UserId = user.UserID,
                    Token = token,
                    CreatedDate = DateTime.Now,
                    ExpiryDate = DateTime.Now.AddHours(TOKEN_EXPIRY_HOURS),
                    IsUsed = false,
                    RequestIpAddress = requestIpAddress
                };

                // Token'ı veritabanına kaydet
                _passwordResetTokenDal.Add(passwordResetToken);

                // Şifre sıfırlama URL'ini oluştur
                var resetUrl = GeneratePasswordResetUrl(token);

                // E-posta gönder
                var emailResult = await _emailService.SendPasswordResetEmailAsync(
                    email, 
                    $"{user.FirstName} {user.LastName}", 
                    token, 
                    resetUrl);

                if (!emailResult.Success)
                {
                    return new ErrorResult("Şifre sıfırlama e-postası gönderilemedi. Lütfen daha sonra tekrar deneyin.");
                }

                return new SuccessResult("Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama bağlantısı gönderilmiştir.");
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"SendPasswordResetEmailAsync Error: {ex.Message}");
                return new ErrorResult("Şifre sıfırlama işlemi sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public IResult ValidatePasswordResetToken(string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    return new ErrorResult("Geçersiz token.");
                }

                var passwordResetToken = _passwordResetTokenDal.GetValidTokenByTokenString(token);
                if (passwordResetToken == null)
                {
                    return new ErrorResult("Geçersiz veya süresi dolmuş token.");
                }

                return new SuccessResult("Token geçerli.");
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"ValidatePasswordResetToken Error: {ex.Message}");
                return new ErrorResult("Token doğrulama sırasında bir hata oluştu.");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult ResetPasswordWithToken(string token, string newPassword, string usedIpAddress = null)
        {
            try
            {
                // Token'ı doğrula
                var tokenValidation = ValidatePasswordResetToken(token);
                if (!tokenValidation.Success)
                {
                    return tokenValidation;
                }

                // Yeni şifre kontrolü
                if (string.IsNullOrEmpty(newPassword) || newPassword.Length < 6)
                {
                    return new ErrorResult("Şifre en az 6 karakter olmalıdır.");
                }

                // IP adresini al
                if (string.IsNullOrEmpty(usedIpAddress))
                {
                    usedIpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
                }

                // Token'ı getir
                var passwordResetToken = _passwordResetTokenDal.GetValidTokenByTokenString(token);
                if (passwordResetToken == null)
                {
                    return new ErrorResult("Geçersiz veya süresi dolmuş token.");
                }

                // Kullanıcıyı getir
                var userResult = _userService.GetById(passwordResetToken.UserId);
                if (!userResult.Success)
                {
                    return new ErrorResult("Kullanıcı bulunamadı.");
                }

                var user = userResult.Data;

                // Yeni şifre hash'i oluştur
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(newPassword, out passwordHash, out passwordSalt);

                // Kullanıcı şifresini güncelle
                user.PasswordHash = passwordHash;
                user.PasswordSalt = passwordSalt;
                user.RequirePasswordChange = false;
                user.UpdatedDate = DateTime.Now;

                var updateResult = _userService.Update(user);
                if (!updateResult.Success)
                {
                    return new ErrorResult("Şifre güncellenirken bir hata oluştu.");
                }

                // Token'ı kullanıldı olarak işaretle
                passwordResetToken.MarkAsUsed(usedIpAddress);
                _passwordResetTokenDal.Update(passwordResetToken);

                // Kullanıcının diğer aktif token'larını da iptal et
                _passwordResetTokenDal.DeactivateAllUserTokens(user.UserID, usedIpAddress);

                return new SuccessResult("Şifreniz başarıyla değiştirildi.");
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"ResetPasswordWithToken Error: {ex.Message}");
                return new ErrorResult("Şifre sıfırlama işlemi sırasında bir hata oluştu.");
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        [TransactionScopeAspect]
        public IResult CancelUserPasswordResetTokens(int userId, string usedIpAddress = null)
        {
            try
            {
                if (userId <= 0)
                {
                    return new ErrorResult("Geçersiz kullanıcı ID'si.");
                }

                if (string.IsNullOrEmpty(usedIpAddress))
                {
                    usedIpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
                }

                _passwordResetTokenDal.DeactivateAllUserTokens(userId, usedIpAddress);
                return new SuccessResult("Şifre sıfırlama token'ları iptal edildi.");
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"CancelUserPasswordResetTokens Error: {ex.Message}");
                return new ErrorResult("Token iptal işlemi sırasında bir hata oluştu.");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public IDataResult<int> CleanupExpiredTokens(int olderThanDays = 7)
        {
            try
            {
                var cleanedCount = _passwordResetTokenDal.CleanupExpiredTokens(olderThanDays);
                return new SuccessDataResult<int>(cleanedCount, $"{cleanedCount} adet süresi dolmuş token temizlendi.");
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                System.Diagnostics.Debug.WriteLine($"CleanupExpiredTokens Error: {ex.Message}");
                return new ErrorDataResult<int>(0, "Token temizleme işlemi sırasında bir hata oluştu.");
            }
        }

        private IResult CheckRateLimit(string ipAddress)
        {
            try
            {
                var sinceDate = DateTime.Now.AddHours(-1); // Son 1 saat
                var requestCount = _passwordResetTokenDal.CountTokenRequestsByIp(ipAddress, sinceDate);

                if (requestCount >= MAX_REQUESTS_PER_HOUR)
                {
                    return new ErrorResult("Çok fazla şifre sıfırlama isteği gönderdiniz. Lütfen 1 saat sonra tekrar deneyin.");
                }

                return new SuccessResult();
            }
            catch (Exception ex)
            {
                // Log the error but allow the operation to continue
                System.Diagnostics.Debug.WriteLine($"CheckRateLimit Error: {ex.Message}");
                return new SuccessResult(); // Rate limit kontrolü başarısız olursa işleme devam et
            }
        }

        private string GenerateSecureToken()
        {
            // Güvenli token oluştur (GUID formatında)
            return Guid.NewGuid().ToString("N"); // 32 karakter, tire olmadan
        }

        private string GeneratePasswordResetUrl(string token)
        {
            // Environment'a göre base URL'i al
            var environment = _configuration["Environment"] ?? "dev";
            var baseUrl = environment switch
            {
                "canlı" => "https://admin.gymkod.com",
                "staging" => "https://staging.gymkod.com",
                _ => "http://localhost:4200"
            };

            return $"{baseUrl}/reset-password?token={token}";
        }
    }
}
