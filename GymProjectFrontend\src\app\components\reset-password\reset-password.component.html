<div class="auth-container">
  <div class="auth-card">
    <!-- Token doğrulama durumu -->
    <div *ngIf="isValidatingToken" class="text-center">
      <div class="spinner-border text-primary mb-3" role="status">
        <span class="visually-hidden">Yükleniyor...</span>
      </div>
      <p>Token doğrulanıyor...</p>
    </div>

    <!-- Token geçersiz -->
    <div *ngIf="!isValidatingToken && !isTokenValid" class="text-center">
      <div class="auth-header">
        <h2 class="text-danger">Geçersiz Bağlantı</h2>
        <p>Bu şifre sıfırlama bağlantısı geçersiz veya süresi dolmuş.</p>
      </div>
      <div class="form-actions">
        <button type="button" class="btn btn-primary btn-block mb-2" (click)="goToForgotPassword()">
          Yeni Şifre Sıfırlama Bağlantısı İste
        </button>
        <button type="button" class="btn btn-outline-secondary btn-block" (click)="goToLogin()">
          Giriş Sayfasına Dön
        </button>
      </div>
    </div>

    <!-- Şifre sıfırlama formu -->
    <div *ngIf="!isValidatingToken && isTokenValid">
      <div class="auth-header">
        <h2>Yeni Şifre Belirle</h2>
        <p>Lütfen yeni şifrenizi girin</p>
      </div>

      <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
        <div class="form-group">
          <label for="newPassword">Yeni Şifre</label>
          <input
            type="password"
            id="newPassword"
            formControlName="newPassword"
            class="form-control"
            [class.is-invalid]="isSubmitted && f['newPassword'].errors"
            placeholder="Yeni şifrenizi girin"
          />
          <div *ngIf="isSubmitted && f['newPassword'].errors" class="invalid-feedback">
            <div *ngIf="f['newPassword'].errors?.['required']">Yeni şifre gereklidir</div>
            <div *ngIf="f['newPassword'].errors?.['minlength']">Şifre en az 6 karakter olmalıdır</div>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Yeni Şifre Tekrar</label>
          <input
            type="password"
            id="confirmPassword"
            formControlName="confirmPassword"
            class="form-control"
            [class.is-invalid]="isSubmitted && f['confirmPassword'].errors"
            placeholder="Yeni şifrenizi tekrar girin"
          />
          <div *ngIf="isSubmitted && f['confirmPassword'].errors" class="invalid-feedback">
            <div *ngIf="f['confirmPassword'].errors?.['required']">Şifre tekrarı gereklidir</div>
            <div *ngIf="f['confirmPassword'].errors?.['passwordMismatch']">Şifreler eşleşmiyor</div>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="submit"
            class="btn btn-primary btn-block"
            [disabled]="isLoading"
          >
            <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
            {{ isLoading ? 'Şifre Değiştiriliyor...' : 'Şifremi Değiştir' }}
          </button>
        </div>

        <div class="auth-footer">
          <p>
            <a href="javascript:void(0)" (click)="goToLogin()" class="auth-link">Giriş Sayfasına Dön</a>
          </p>
        </div>
      </form>
    </div>
  </div>
</div>
